# 串口输出格式说明

## 概述

系统现在支持串口输出频率和归一化后的电压峰峰值比，并提供三种不同的输出模式以满足不同需求。

## 输出模式

### 模式 0：简单模式 (默认)
**用途**: 标准测量输出，适用于数据采集和分析
**格式**: `频率(Hz)\t归一化峰峰值比\r\n`
**示例**:
```
1000.0	0.856432
1100.0	0.923156
1200.0	0.987654
```

### 模式 1：详细模式
**用途**: 需要更多信息进行分析时使用
**格式**: `频率\t归一化比值\t原始Vpp1\t原始Vpp2\t相位差\r\n`
**示例**:
```
1000.0	0.856432	2.145000	2.506000	-15.23
1100.0	0.923156	2.234000	2.421000	-12.45
1200.0	0.987654	2.456000	2.487000	-8.76
```

### 模式 2：调试模式
**用途**: 调试和故障排除
**格式**: 带标签的详细信息
**示例**:
```
F=1000.0	Norm=0.856432	Vpp1=2.145000	Vpp2=2.506000	Phase=-15.23	SNR=45.2
F=1100.0	Norm=0.923156	Vpp1=2.234000	Vpp2=2.421000	Phase=-12.45	SNR=47.8
```

## 数据说明

### 频率 (Hz)
- 当前测试频率
- 精度: 0.1 Hz
- 范围: 根据系统设置

### 归一化峰峰值比
- ADC1峰峰值 / ADC2峰峰值 的归一化结果
- 归一化基准: 扫频过程中的最大比值
- 精度: 6位小数
- 范围: 0.0 ~ 1.0

### 原始峰峰值 (Vpp1, Vpp2)
- ADC1和ADC2的实际峰峰值电压
- 单位: 伏特 (V)
- 精度: 6位小数
- 计算方法: RMS × 2√2 (适用于正弦波)

### 相位差 (度)
- ADC1相对于ADC2的相位差
- 单位: 度 (°)
- 精度: 0.01度
- 范围: -180° ~ +180°

### 信噪比 (SNR)
- 估计的信噪比
- 单位: dB
- 精度: 0.1 dB
- 用于算法自适应选择

## 使用方法

### 在代码中切换输出模式
```c
// 设置为简单模式
SetOutputMode(OUTPUT_MODE_SIMPLE);

// 设置为详细模式  
SetOutputMode(OUTPUT_MODE_DETAILED);

// 设置为调试模式
SetOutputMode(OUTPUT_MODE_DEBUG);
```

### 查看帮助信息
```c
PrintOutputModeHelp();
```

### 通过串口命令切换 (如果实现了命令解析)
```
MODE 0  // 简单模式
MODE 1  // 详细模式
MODE 2  // 调试模式
HELP    // 显示帮助
```

## 数据处理建议

### 简单模式数据处理
```python
# Python示例
import numpy as np
import matplotlib.pyplot as plt

# 读取数据
data = []
with open('sweep_data.txt', 'r') as f:
    for line in f:
        freq, ratio = line.strip().split('\t')
        data.append([float(freq), float(ratio)])

data = np.array(data)
frequencies = data[:, 0]
ratios = data[:, 1]

# 绘制频率响应
plt.semilogx(frequencies, 20*np.log10(ratios))
plt.xlabel('Frequency (Hz)')
plt.ylabel('Magnitude (dB)')
plt.title('Filter Frequency Response')
plt.grid(True)
plt.show()
```

### 详细模式数据处理
```python
# 包含相位信息的处理
with open('detailed_data.txt', 'r') as f:
    for line in f:
        parts = line.strip().split('\t')
        freq = float(parts[0])
        ratio = float(parts[1])
        vpp1 = float(parts[2])
        vpp2 = float(parts[3])
        phase = float(parts[4])
        
        # 处理幅度和相位数据
        magnitude_db = 20 * np.log10(ratio)
        # ... 进一步处理
```

## 注意事项

1. **数据同步**: 确保在扫频完成后再处理数据
2. **归一化**: 第一阶段扫频用于找最大值，第二阶段才输出归一化数据
3. **精度**: 峰峰值计算基于RMS值，适用于正弦波信号
4. **相位**: 相位测量使用优化算法，在高信噪比时更准确
5. **格式**: 使用制表符(\t)分隔，便于Excel等工具导入

## 故障排除

### 数据异常
- 检查信号连接
- 验证信号幅度是否在ADC范围内
- 确认采样率设置正确

### 相位测量不准确
- 检查信号质量和信噪比
- 确认信号频率在测量范围内
- 考虑使用调试模式查看SNR值

### 归一化异常
- 确保完成了第一阶段扫频
- 检查最大值是否正确找到
- 验证扫频范围设置

## 输出示例文件

### sweep_simple.txt (简单模式)
```
100.0	0.123456
200.0	0.234567
300.0	0.345678
...
```

### sweep_detailed.txt (详细模式)
```
100.0	0.123456	1.234567	2.345678	-45.67
200.0	0.234567	1.345678	2.456789	-38.92
300.0	0.345678	1.456789	2.567890	-32.15
...
```
