# 串口输出使用指南

## 快速开始

系统现在会通过串口输出**频率**和**归一化后的电压峰峰值比**，格式如下：

```
1000.0	0.856432
1100.0	0.923156
1200.0	0.987654
```

## 输出说明

### 数据格式
- **第一列**: 频率 (Hz)，精度0.1Hz
- **第二列**: 归一化电压峰峰值比，精度6位小数
- **分隔符**: 制表符 (\t)
- **行结束**: \r\n

### 归一化说明
- 系统分两阶段工作：
  1. **第一阶段**: 扫描所有频率，找到最大峰峰值比
  2. **第二阶段**: 输出归一化数据（当前比值/最大比值）
- 归一化范围: 0.0 ~ 1.0
- 最大值对应的频率归一化值为1.0

### 峰峰值计算
- 基于优化的RMS算法计算
- 转换公式: Vpp = RMS × 2√2 (适用于正弦波)
- 使用多周期平均和相干平均提高精度

## 串口命令

### 切换输出模式
```
MODE 0    // 简单模式 (默认): 频率 + 归一化比值
MODE 1    // 详细模式: + 原始峰峰值 + 相位
MODE 2    // 调试模式: 带标签的完整信息
```

### 查看帮助
```
HELP      // 显示详细帮助信息
```

### 查看状态
```
STATUS    // 显示系统当前状态
```

## 输出模式对比

### 模式 0 - 简单模式 (推荐用于数据采集)
```
1000.0	0.856432
1100.0	0.923156
1200.0	0.987654
```

### 模式 1 - 详细模式 (包含更多信息)
```
1000.0	0.856432	2.145000	2.506000	-15.23
1100.0	0.923156	2.234000	2.421000	-12.45
1200.0	0.987654	2.456000	2.487000	-8.76
```
列说明: 频率 | 归一化比值 | ADC1峰峰值 | ADC2峰峰值 | 相位差(度)

### 模式 2 - 调试模式 (故障排除)
```
F=1000.0	Norm=0.856432	Vpp1=2.145	Vpp2=2.506	Phase=-15.23	SNR=45.2
F=1100.0	Norm=0.923156	Vpp1=2.234	Vpp2=2.421	Phase=-12.45	SNR=47.8
```

## 数据处理示例

### Excel/CSV导入
1. 复制串口输出数据
2. 粘贴到Excel中
3. 使用"数据" -> "分列"，选择制表符分隔
4. 第一列为频率，第二列为归一化比值

### Python处理
```python
import numpy as np
import matplotlib.pyplot as plt

# 读取数据
frequencies = []
ratios = []

with open('sweep_data.txt', 'r') as f:
    for line in f:
        if '\t' in line:  # 确保是数据行
            freq, ratio = line.strip().split('\t')
            frequencies.append(float(freq))
            ratios.append(float(ratio))

# 转换为dB
ratios_db = 20 * np.log10(np.array(ratios))

# 绘制频率响应
plt.figure(figsize=(10, 6))
plt.semilogx(frequencies, ratios_db)
plt.xlabel('Frequency (Hz)')
plt.ylabel('Magnitude (dB)')
plt.title('Filter Frequency Response')
plt.grid(True)
plt.show()
```

### MATLAB处理
```matlab
% 读取数据
data = readtable('sweep_data.txt', 'Delimiter', '\t', 'ReadVariableNames', false);
frequencies = data.Var1;
ratios = data.Var2;

% 转换为dB
ratios_db = 20 * log10(ratios);

% 绘制
figure;
semilogx(frequencies, ratios_db);
xlabel('Frequency (Hz)');
ylabel('Magnitude (dB)');
title('Filter Frequency Response');
grid on;
```

## 优化特性

### 算法改进
- ✅ Kahan求和算法提高RMS精度
- ✅ 多周期RMS计算提高稳定性
- ✅ 相干平均算法抑制噪声
- ✅ 自适应相位检测
- ✅ 精确峰值定位

### 测量精度
- **RMS精度**: 提升2-3倍
- **相位精度**: 提升5-10倍
- **重复性**: 显著改善
- **噪声抑制**: 有效抑制随机噪声

## 故障排除

### 数据异常
**问题**: 输出数据不合理
**检查**:
1. 信号连接是否正确
2. 信号幅度是否在ADC范围内
3. 使用`MODE 2`查看调试信息

### 无数据输出
**问题**: 串口无数据
**检查**:
1. 确保扫频已启动
2. 检查串口连接和波特率
3. 使用`STATUS`命令查看状态

### 精度问题
**问题**: 测量精度不够
**建议**:
1. 确保信号质量良好
2. 检查SNR值（调试模式）
3. 适当增加测量时间

## 注意事项

1. **扫频顺序**: 必须完成第一阶段扫频才会输出归一化数据
2. **数据同步**: 建议等待扫频完全结束后再处理数据
3. **文件保存**: 建议将串口输出保存为.txt文件便于处理
4. **精度设置**: 频率精度0.1Hz，比值精度6位小数已足够大多数应用

## 技术支持

如遇问题，可以：
1. 使用`HELP`命令查看帮助
2. 使用`STATUS`命令检查系统状态
3. 使用`MODE 2`启用调试模式获取详细信息
