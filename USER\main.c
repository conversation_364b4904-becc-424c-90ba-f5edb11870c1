#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// ADC1采样数据存储 - 优化内存使用
#define ADC1_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

// ADC2采样数据存储 - 优化内存使用
#define ADC2_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc2_sample_buffer[ADC2_SAMPLE_SIZE];  // ADC2采样数据缓冲区
volatile uint16_t adc2_sample_index = 0;       // 当前采样索引
volatile uint8_t adc2_sampling_complete = 0;   // 采样完成标志

char lcd_buffer[50];              // LCD显示缓冲区

// 输出模式定义
#define OUTPUT_MODE_SIMPLE    0  // 只输出频率和归一化峰峰值比
#define OUTPUT_MODE_DETAILED  1  // 输出详细信息
#define OUTPUT_MODE_DEBUG     2  // 输出调试信息

uint8_t output_mode = OUTPUT_MODE_SIMPLE; // 默认简单输出模式

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC2采样控制函数声明
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
void ADC2_ResetSampling(void);

// 优化的信号分析函数声明
float CalculateOptimizedRMS(uint16_t* buffer, uint32_t size, float dc_offset);
float FindPrecisePeakPosition(float32_t* signal, uint32_t size, float sample_rate, float target_freq);
float CalculatePhaseFromZeroCrossing(float32_t* sig1, float32_t* sig2, uint32_t size, float sample_rate, float freq);
void ApplyMovingAverageFilter(float32_t* input, float32_t* output, uint32_t size, uint32_t window_size);
float CalculateMultiCycleRMS(float32_t* signal, uint32_t size, float sample_rate, float freq);
float CalculateCoherentAverage(float32_t* signal, uint32_t size, float sample_rate, float freq);
void PrintMeasurementDiagnostics(float freq, float adc1_rms, float adc2_rms, float phase, float snr);
void SetOutputMode(uint8_t mode);
void PrintOutputModeHelp(void);
void ProcessSerialCommand(char* command);

// 优化的FFT-based幅度和相位检测函数声明
typedef struct {
    float amplitude;
    float phase_deg;
    float frequency;
    float snr_db;
} SignalAnalysisResult;

SignalAnalysisResult AnalyzeSignalFFT(float32_t* signal_buffer, uint32_t buffer_size,
                                     float sample_rate, float target_freq);
float InterpolatePhase(float32_t* fft_complex, uint32_t peak_bin, uint32_t fft_size);

// 扫频测试相关变量和函数声明
typedef struct {
    float frequency;        // 当前频率
    float adc1_amplitude;   // ADC1峰峰值（滤波器输出）
    float adc2_amplitude;   // ADC2峰峰值（滤波器输入）
    float magnitude_db;     // 幅度响应(dB)
    float phase_deg;        // 相位响应(度)
} FrequencyResponse;

#define SWEEP_POINTS 1996   // 扫频点数：(400kHz-1kHz)/200Hz + 1 = 1996
#define SWEEP_BUFFER_SIZE 50  // 只保存最近50个点的结果用于分析
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];  // 减少内存使用：50×20字节=1KB
volatile uint8_t sweep_test_active = 0;
volatile uint16_t current_sweep_point = 0;
volatile uint8_t sweep_sampling_complete = 0;
volatile uint16_t total_sweep_points = 0;  // 总扫频点数计数器

// 归一化处理相关变量
float max_voltage_ratio = 0.0f;           // 最大电压幅度比
volatile uint8_t sweep_phase = 0;          // 扫频阶段：0=寻找最大值，1=归一化输出

// 扫频测试函数声明
void StartSweepTest(void);
void StopSweepTest(void);
void ProcessSweepPoint(void);
void OutputSweepResults(void);
void AnalyzeFilterCharacteristics(void);

// 扫频测试函数声明（简化版）

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义七个按钮 - 更大尺寸便于操作
Button_t buttons[7] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "SWEEP OFF", 0.0f,     GRAY}      // 扫频测试按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 7; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 7; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();     // ADC2配置为PC1引脚（ADC123_IN11通道11）
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    Adc3_Init();

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    // 扫频测试使用中断方式采样，不需要DMA
    // DMA1_Init();  // ADC1使用中断采样，不需要DMA
    // DMA2_Init();  // ADC2使用中断采样，不需要DMA
    // DMA3_Init();  // ADC3也使用中断采样，不需要DMA
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    lcd_init();
   
    sampfre = 815534;  // 实际采样频率：84MHz / 103 / 1 = 815534Hz

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz ≈ 819200Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // 初始化TIM6用于DAC正弦波输出 (100kHz中断频率)
    // 84MHz / (84-1) / (10-1) = 100kHz
    TIM6_DAC_Init(10 - 1, 84 - 1);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 7;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮 - 启动扫频测试
                    if (!sweep_test_active) {
                        // 启动扫频测试
                        sprintf(buttons[6].text, "SWEEP ON");
                        buttons[6].color = GREEN;

                        // 启动扫频测试（无串口输出）

                        // 关闭DAC
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            dac_enable_changed = 1;
                            sprintf(buttons[4].text, "DAC OFF");
                            buttons[4].color = GRAY;
                        }

                        // 启动扫频测试
                        StartSweepTest();

                    } else {
                        // 停止扫频测试
                        sprintf(buttons[6].text, "SWEEP OFF");
                        buttons[6].color = GRAY;

                        StopSweepTest();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 3000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>3kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 处理扫频测试
        if (sweep_test_active)
        {
            // 检查当前频率点的采样是否完成
            if (adc1_sampling_complete && adc2_sampling_complete)
            {
                // 处理当前扫频点的数据
                ProcessSweepPoint();

                // 移动到下一个频率点
                current_sweep_point++;

                if (current_sweep_point < SWEEP_POINTS)
                {
                    // 设置下一个频率
                    float next_freq = 1000.0f + current_sweep_point * 200.0f;
                    AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);

                    // 显示进度
                    char progress_info[100];
                    sprintf(progress_info, "Sweep: %d/%d (%.1fkHz)",
                            current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                    lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                    // 重新启动ADC采样
                    ADC1_ResetSampling();
                    ADC2_ResetSampling();
                    ADC1_StartSampling();
                    ADC2_StartSampling();
                }
                else
                {
                    if (sweep_phase == 0) {
                        // 第一阶段完成，开始第二阶段
                        sweep_phase = 1;
                        current_sweep_point = 0;

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(10);

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 2: Normalized Output", BLUE);
                    } else {
                        // 第二阶段完成，扫频测试结束
                        StopSweepTest();
                        OutputSweepResults();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Test Complete!", GREEN);
                    }
                }
            }
        }

        // 检查ADC1采样是否完成（非扫频模式）
        if (adc1_sampling_complete && !sweep_test_active && adc_user_enabled)
        {
            // 显示采样完成信息
            char sample_info[100];
            sprintf(sample_info, "ADC1: 4096 samples complete");
            lcd_show_string(10, 90, lcddev.width, 20, 12, sample_info, BLUE);

            // 显示一些采样数据（前几个点）
            sprintf(sample_info, "Data[0-3]: %d %d %d %d",
                    adc1_sample_buffer[0], adc1_sample_buffer[1],
                    adc1_sample_buffer[2], adc1_sample_buffer[3]);
            lcd_show_string(10, 110, lcddev.width, 20, 12, sample_info, GREEN);

            // 通过串口输出所有ADC1采样数据
            printf("ADC1_SAMPLES_START\r\n");
            for (int i = 0; i < ADC1_SAMPLE_SIZE; i++)
            {
                printf("%d\t", adc1_sample_buffer[i]);
            }
            printf("ADC1_SAMPLES_END\r\n");

            // 重置采样状态，准备下次采样
            adc1_sampling_complete = 0;
            adc1_sample_index = 0;
        }

        delay_ms(10);  // 主循环延时
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ADC2采样控制函数实现
void ADC2_StartSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }

    // 启动ADC2
    ADC_Cmd(ADC2, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC2_StopSampling(void)
{
    // 停止ADC2
    ADC_Cmd(ADC2, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC2_ResetSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }
}

// 扫频测试函数实现
void StartSweepTest(void)
{
    sweep_test_active = 1;
    current_sweep_point = 0;
    total_sweep_points = 0;
    sweep_sampling_complete = 0;
    max_voltage_ratio = 0.0f;
    sweep_phase = 0;  // 第一阶段：寻找最大值

    // 清空结果缓冲区
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        sweep_results[i].frequency = 0;
        sweep_results[i].adc1_amplitude = 0;
        sweep_results[i].adc2_amplitude = 0;
        sweep_results[i].magnitude_db = 0;
        sweep_results[i].phase_deg = 0;
    }

    // 设置起始频率 1kHz
    float start_freq = 1000.0f;
    AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);

    // 开始扫频测试，无串口输出

    // 等待频率稳定
    delay_ms(10);

    // 启动ADC1和ADC2同步采样
    ADC1_ResetSampling();
    ADC2_ResetSampling();
    ADC1_StartSampling();
    ADC2_StartSampling();
}

void StopSweepTest(void)
{
    sweep_test_active = 0;

    // 停止ADC采样
    ADC1_StopSampling();
    ADC2_StopSampling();
}

void ProcessSweepPoint(void)
{
    float current_freq = 1000.0f + current_sweep_point * 200.0f;

    // 优化的RMS计算和相位检测算法
    float adc1_dc = 0, adc2_dc = 0;
    float adc1_rms = 0, adc2_rms = 0;

    // 使用ARM DSP库计算均值（直流分量）
    arm_mean_f32((float32_t*)adc1_sample_buffer, ADC1_SAMPLE_SIZE, &adc1_dc);
    arm_mean_f32((float32_t*)adc2_sample_buffer, ADC2_SAMPLE_SIZE, &adc2_dc);

    // 将ADC数据转换为电压值进行计算（提高精度）
    const float adc_to_voltage = 3.3f / 4095.0f; // ADC转电压系数
    float adc1_dc_voltage = adc1_dc * adc_to_voltage;
    float adc2_dc_voltage = adc2_dc * adc_to_voltage;

    // 使用优化的RMS计算函数（基于电压值）
    float adc1_rms_basic = CalculateOptimizedRMS(adc1_sample_buffer, ADC1_SAMPLE_SIZE, adc1_dc) * adc_to_voltage;
    float adc2_rms_basic = CalculateOptimizedRMS(adc2_sample_buffer, ADC2_SAMPLE_SIZE, adc2_dc) * adc_to_voltage;

    // 初始设置RMS值
    adc1_rms = adc1_rms_basic;
    adc2_rms = adc2_rms_basic;

    // 创建临时缓冲区用于去直流分量的数据
    static float32_t adc1_ac_buffer[1024];
    static float32_t adc2_ac_buffer[1024];
    static float32_t adc1_filtered[1024];
    static float32_t adc2_filtered[1024];

    // 去除直流分量并转换为电压值
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_ac_buffer[i] = ((float)adc1_sample_buffer[i] - adc1_dc) * adc_to_voltage;
        adc2_ac_buffer[i] = ((float)adc2_sample_buffer[i] - adc2_dc) * adc_to_voltage;
    }

    // 应用轻微的移动平均滤波以减少噪声
    uint32_t filter_window = 3; // 小窗口以保持信号特征
    ApplyMovingAverageFilter(adc1_ac_buffer, adc1_filtered, ADC1_SAMPLE_SIZE, filter_window);
    ApplyMovingAverageFilter(adc2_ac_buffer, adc2_filtered, ADC2_SAMPLE_SIZE, filter_window);

    // 如果频率已知且稳定，使用多周期算法提高精度
    if (current_freq > 10.0f && current_freq < 100000.0f) {
        float sample_rate = 815534.0f;
        float samples_per_period = sample_rate / current_freq;

        // 如果有足够的周期数据，使用多周期算法
        if (samples_per_period > 10 && samples_per_period < ADC1_SAMPLE_SIZE / 3) {
            float adc1_multicycle_rms = CalculateMultiCycleRMS(adc1_filtered, ADC1_SAMPLE_SIZE, sample_rate, current_freq);
            float adc2_multicycle_rms = CalculateMultiCycleRMS(adc2_filtered, ADC2_SAMPLE_SIZE, sample_rate, current_freq);

            // 使用相干平均进一步提高精度
            float adc1_coherent = CalculateCoherentAverage(adc1_filtered, ADC1_SAMPLE_SIZE, sample_rate, current_freq);
            float adc2_coherent = CalculateCoherentAverage(adc2_filtered, ADC2_SAMPLE_SIZE, sample_rate, current_freq);

            // 根据信号质量选择最佳算法
            if (adc1_coherent > 0 && adc2_coherent > 0) {
                // 相干平均成功，使用相干平均结果
                adc1_rms = adc1_coherent;
                adc2_rms = adc2_coherent;
            } else if (adc1_multicycle_rms > 0 && adc2_multicycle_rms > 0) {
                // 使用多周期RMS
                adc1_rms = adc1_multicycle_rms;
                adc2_rms = adc2_multicycle_rms;
            }
            // 否则保持使用基本RMS值
        }
    }

    // 计算幅度响应 (dB) - 添加小的偏移量避免log(0)
    float magnitude_db;
    const float min_value = 1e-6f;
    if (adc2_rms > min_value && adc1_rms > min_value) {
        magnitude_db = 20.0f * log10f(adc1_rms / adc2_rms);
    } else {
        magnitude_db = -120.0f; // 更合理的最小值
    }

    // 优化的相位检测算法 - 结合过零点和互相关方法
    float phase_deg = 0.0f;
    float sample_rate = 815534.0f;

    // 方法1：基于过零点的相位检测（适用于高信噪比）
    float phase_zero_cross = CalculatePhaseFromZeroCrossing(adc1_filtered, adc2_filtered,
                                                           ADC1_SAMPLE_SIZE, sample_rate, current_freq);

    // 方法2：基于精确峰值位置的相位检测
    float peak1_pos = FindPrecisePeakPosition(adc1_filtered, ADC1_SAMPLE_SIZE, sample_rate, current_freq);
    float peak2_pos = FindPrecisePeakPosition(adc2_filtered, ADC2_SAMPLE_SIZE, sample_rate, current_freq);

    float samples_per_period = sample_rate / current_freq;
    float phase_peak = ((peak1_pos - peak2_pos) / samples_per_period) * 360.0f;

    // 限制相位范围
    while (phase_peak > 180.0f) phase_peak -= 360.0f;
    while (phase_peak < -180.0f) phase_peak += 360.0f;

    // 根据信号质量选择最佳相位检测方法
    float snr_estimate = 20.0f * log10f(adc1_rms / (adc1_rms * 0.01f + 1e-6f)); // 简单的SNR估计

    if (snr_estimate > 40.0f) {
        // 高信噪比时使用过零点方法
        phase_deg = phase_zero_cross;
    } else {
        // 低信噪比时使用峰值方法
        phase_deg = phase_peak;
    }

    // 如果两种方法差异很大，使用平均值
    float phase_diff = fabsf(phase_zero_cross - phase_peak);
    if (phase_diff > 180.0f) phase_diff = 360.0f - phase_diff;

    if (phase_diff > 30.0f && snr_estimate > 20.0f) {
        // 差异较大时使用加权平均
        float weight = (snr_estimate - 20.0f) / 20.0f;
        if (weight > 1.0f) weight = 1.0f;
        phase_deg = phase_zero_cross * weight + phase_peak * (1.0f - weight);

        // 重新限制相位范围
        while (phase_deg > 180.0f) phase_deg -= 360.0f;
        while (phase_deg < -180.0f) phase_deg += 360.0f;
    }

    // 可选：打印诊断信息（调试时启用）
    // PrintMeasurementDiagnostics(current_freq, adc1_rms, adc2_rms, phase_deg, snr_estimate);

    // 将RMS值转换为峰峰值 (Vpp = RMS * 2 * sqrt(2) 对于正弦波)
    const float rms_to_pp_factor = 2.0f * 1.414213562f; // 2 * sqrt(2)
    float adc1_vpp = adc1_rms * rms_to_pp_factor;
    float adc2_vpp = adc2_rms * rms_to_pp_factor;

    // 计算电压峰峰值比（线性比值，不是dB）
    float voltage_pp_ratio = 0.0f;
    if (adc2_vpp > 0) {
        voltage_pp_ratio = adc1_vpp / adc2_vpp;
    }

    if (sweep_phase == 0) {
        // 第一阶段：寻找最大值，不输出
        if (voltage_pp_ratio > max_voltage_ratio) {
            max_voltage_ratio = voltage_pp_ratio;
        }
    } else {
        // 第二阶段：归一化输出
        float normalized_pp_ratio = 0.0f;

        if (max_voltage_ratio > 0.0f) {
            normalized_pp_ratio = voltage_pp_ratio / max_voltage_ratio;
        }

        // 根据输出模式选择输出格式
        switch (output_mode) {
            case OUTPUT_MODE_SIMPLE:
                // 简单模式：只输出频率和归一化峰峰值比
                printf("%.1f\t%.6f\r\n", current_freq, normalized_pp_ratio);
                break;

            case OUTPUT_MODE_DETAILED:
                // 详细模式：输出频率、归一化峰峰值比、原始峰峰值、相位
                printf("%.1f\t%.6f\t%.6f\t%.6f\t%.2f\r\n",
                       current_freq, normalized_pp_ratio, adc1_vpp, adc2_vpp, phase_deg);
                break;

            case OUTPUT_MODE_DEBUG:
                // 调试模式：输出所有信息
                printf("F=%.1f\tNorm=%.6f\tVpp1=%.6f\tVpp2=%.6f\tPhase=%.2f\tSNR=%.1f\r\n",
                       current_freq, normalized_pp_ratio, adc1_vpp, adc2_vpp, phase_deg,
                       20.0f * log10f(adc1_rms / (adc1_rms * 0.01f + 1e-6f)));
                break;

            default:
                // 默认使用简单模式
                printf("%.1f\t%.6f\r\n", current_freq, normalized_pp_ratio);
                break;
        }
    }

    // 只在缓冲区中保存最近的数据用于分析
    int buffer_idx = current_sweep_point % SWEEP_BUFFER_SIZE;
    sweep_results[buffer_idx].frequency = current_freq;
    sweep_results[buffer_idx].adc1_amplitude = adc1_vpp; // 保存峰峰值
    sweep_results[buffer_idx].adc2_amplitude = adc2_vpp; // 保存峰峰值
    sweep_results[buffer_idx].magnitude_db = magnitude_db;
    sweep_results[buffer_idx].phase_deg = phase_deg;

    total_sweep_points++;
}

void OutputSweepResults(void)
{
    // 扫频完成，无额外输出
}

void AnalyzeFilterCharacteristics(void)
{
    printf("=== FILTER ANALYSIS (Based on Recent Data) ===\r\n");

    // 分析缓冲区中的数据
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    if (valid_points < 5) {
        printf("Insufficient data for analysis\r\n");
        printf("=== END OF ANALYSIS ===\r\n");
        return;
    }

    // 找到最大和最小幅度
    float max_magnitude = -100.0f;
    float min_magnitude = 100.0f;
    float max_freq = 0, min_freq = 0;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {  // 有效数据
            if (sweep_results[i].magnitude_db > max_magnitude) {
                max_magnitude = sweep_results[i].magnitude_db;
                max_freq = sweep_results[i].frequency;
            }
            if (sweep_results[i].magnitude_db < min_magnitude) {
                min_magnitude = sweep_results[i].magnitude_db;
                min_freq = sweep_results[i].frequency;
            }
        }
    }

    printf("Recent Data Analysis:\r\n");
    printf("Max Gain: %.2f dB at %.1f Hz\r\n", max_magnitude, max_freq);
    printf("Min Gain: %.2f dB at %.1f Hz\r\n", min_magnitude, min_freq);
    printf("Gain Range: %.2f dB\r\n", max_magnitude - min_magnitude);

    // 简单的滤波器类型判断
    float gain_variation = max_magnitude - min_magnitude;
    if (gain_variation > 10.0f) {
        printf("Filter Type: Significant frequency response variation detected\r\n");
    } else if (max_magnitude > -1.0f) {
        printf("Filter Type: Likely passband region\r\n");
    } else if (max_magnitude < -10.0f) {
        printf("Filter Type: Likely stopband region\r\n");
    } else {
        printf("Filter Type: Transition region\r\n");
    }

    printf("Note: Complete analysis requires full sweep data\r\n");
    printf("=== END OF ANALYSIS ===\r\n");
}

/**
 * @brief 优化的RMS计算函数
 * @param buffer: ADC采样缓冲区
 * @param size: 缓冲区大小
 * @param dc_offset: 直流偏移量
 * @retval RMS值
 */
float CalculateOptimizedRMS(uint16_t* buffer, uint32_t size, float dc_offset)
{
    float sum_squares = 0.0f;
    float sample;

    // 使用Kahan求和算法提高精度
    float compensation = 0.0f;

    for (uint32_t i = 0; i < size; i++) {
        sample = (float)buffer[i] - dc_offset;
        float square = sample * sample;

        // Kahan求和
        float y = square - compensation;
        float t = sum_squares + y;
        compensation = (t - sum_squares) - y;
        sum_squares = t;
    }

    return sqrtf(sum_squares / size);
}

/**
 * @brief 使用抛物线插值找到精确的峰值位置
 * @param signal: 信号缓冲区
 * @param size: 缓冲区大小
 * @param sample_rate: 采样率
 * @param target_freq: 目标频率
 * @retval 精确的峰值位置（浮点数索引）
 */
float FindPrecisePeakPosition(float32_t* signal, uint32_t size, float sample_rate, float target_freq)
{
    // 首先找到粗略的峰值位置
    uint32_t rough_peak = 0;
    float max_val = 0.0f;

    for (uint32_t i = 1; i < size - 1; i++) {
        if (fabsf(signal[i]) > max_val) {
            max_val = fabsf(signal[i]);
            rough_peak = i;
        }
    }

    // 使用抛物线插值精确定位峰值
    if (rough_peak > 0 && rough_peak < size - 1) {
        float y1 = fabsf(signal[rough_peak - 1]);
        float y2 = fabsf(signal[rough_peak]);
        float y3 = fabsf(signal[rough_peak + 1]);

        // 抛物线插值公式
        float a = (y1 - 2*y2 + y3) / 2.0f;
        float b = (y3 - y1) / 2.0f;

        if (fabsf(a) > 1e-6f) {
            float offset = -b / (2.0f * a);
            // 限制偏移量在合理范围内
            if (offset > -1.0f && offset < 1.0f) {
                return rough_peak + offset;
            }
        }
    }

    return (float)rough_peak;
}

/**
 * @brief 基于过零点计算相位差
 * @param sig1: 第一个信号
 * @param sig2: 第二个信号
 * @param size: 信号长度
 * @param sample_rate: 采样率
 * @param freq: 信号频率
 * @retval 相位差（度）
 */
float CalculatePhaseFromZeroCrossing(float32_t* sig1, float32_t* sig2, uint32_t size, float sample_rate, float freq)
{
    // 寻找第一个信号的上升沿过零点
    float zero_cross1 = -1.0f;
    float zero_cross2 = -1.0f;

    for (uint32_t i = 1; i < size - 1; i++) {
        // 寻找sig1的上升沿过零点
        if (zero_cross1 < 0 && sig1[i-1] < 0 && sig1[i] >= 0) {
            // 线性插值找到精确的过零点
            float t = -sig1[i-1] / (sig1[i] - sig1[i-1]);
            zero_cross1 = i - 1 + t;
        }

        // 寻找sig2的上升沿过零点
        if (zero_cross2 < 0 && sig2[i-1] < 0 && sig2[i] >= 0) {
            // 线性插值找到精确的过零点
            float t = -sig2[i-1] / (sig2[i] - sig2[i-1]);
            zero_cross2 = i - 1 + t;
        }

        // 如果都找到了，就可以计算相位差
        if (zero_cross1 >= 0 && zero_cross2 >= 0) {
            break;
        }
    }

    if (zero_cross1 >= 0 && zero_cross2 >= 0) {
        float time_diff = (zero_cross1 - zero_cross2) / sample_rate;
        float period = 1.0f / freq;
        float phase_deg = (time_diff / period) * 360.0f;

        // 限制相位范围
        while (phase_deg > 180.0f) phase_deg -= 360.0f;
        while (phase_deg < -180.0f) phase_deg += 360.0f;

        return phase_deg;
    }

    return 0.0f; // 如果找不到过零点，返回0
}

/**
 * @brief 移动平均滤波器
 * @param input: 输入信号
 * @param output: 输出信号
 * @param size: 信号长度
 * @param window_size: 滤波窗口大小
 */
void ApplyMovingAverageFilter(float32_t* input, float32_t* output, uint32_t size, uint32_t window_size)
{
    if (window_size == 0 || window_size > size) {
        // 如果窗口大小无效，直接复制
        for (uint32_t i = 0; i < size; i++) {
            output[i] = input[i];
        }
        return;
    }

    float sum = 0.0f;
    uint32_t half_window = window_size / 2;

    // 初始化窗口
    for (uint32_t i = 0; i < window_size && i < size; i++) {
        sum += input[i];
    }

    for (uint32_t i = 0; i < size; i++) {
        if (i >= half_window && i < size - half_window) {
            output[i] = sum / window_size;

            // 滑动窗口
            if (i + half_window + 1 < size) {
                sum = sum - input[i - half_window] + input[i + half_window + 1];
            }
        } else {
            // 边界处理
            output[i] = input[i];
        }
    }
}

/**
 * @brief 多周期RMS计算，提高测量精度
 * @param signal: 信号缓冲区
 * @param size: 缓冲区大小
 * @param sample_rate: 采样率
 * @param freq: 信号频率
 * @retval 多周期平均RMS值
 */
float CalculateMultiCycleRMS(float32_t* signal, uint32_t size, float sample_rate, float freq)
{
    float samples_per_period = sample_rate / freq;
    uint32_t period_samples = (uint32_t)(samples_per_period + 0.5f);

    if (period_samples == 0 || period_samples >= size) {
        // 如果周期太长，使用传统RMS计算
        float rms = 0.0f;
        arm_rms_f32(signal, size, &rms);
        return rms;
    }

    // 计算完整周期数
    uint32_t num_complete_periods = size / period_samples;
    if (num_complete_periods < 2) {
        // 如果少于2个完整周期，使用传统方法
        float rms = 0.0f;
        arm_rms_f32(signal, size, &rms);
        return rms;
    }

    float total_rms = 0.0f;

    // 对每个完整周期计算RMS
    for (uint32_t period = 0; period < num_complete_periods; period++) {
        uint32_t start_idx = period * period_samples;
        float period_rms = 0.0f;

        arm_rms_f32(&signal[start_idx], period_samples, &period_rms);
        total_rms += period_rms * period_rms; // 累加功率
    }

    // 返回平均RMS
    return sqrtf(total_rms / num_complete_periods);
}

/**
 * @brief 相干平均算法，减少噪声影响
 * @param signal: 信号缓冲区
 * @param size: 缓冲区大小
 * @param sample_rate: 采样率
 * @param freq: 信号频率
 * @retval 相干平均后的信号幅度
 */
float CalculateCoherentAverage(float32_t* signal, uint32_t size, float sample_rate, float freq)
{
    float samples_per_period = sample_rate / freq;
    uint32_t period_samples = (uint32_t)(samples_per_period + 0.5f);

    if (period_samples == 0 || period_samples >= size) {
        return 0.0f;
    }

    uint32_t num_complete_periods = size / period_samples;
    if (num_complete_periods < 2) {
        return 0.0f;
    }

    // 创建平均周期缓冲区
    static float32_t averaged_period[512]; // 假设最大周期长度
    if (period_samples > 512) {
        period_samples = 512;
    }

    // 初始化平均缓冲区
    for (uint32_t i = 0; i < period_samples; i++) {
        averaged_period[i] = 0.0f;
    }

    // 累加所有周期
    for (uint32_t period = 0; period < num_complete_periods; period++) {
        uint32_t start_idx = period * period_samples;
        for (uint32_t i = 0; i < period_samples; i++) {
            averaged_period[i] += signal[start_idx + i];
        }
    }

    // 计算平均
    for (uint32_t i = 0; i < period_samples; i++) {
        averaged_period[i] /= num_complete_periods;
    }

    // 计算平均周期的RMS
    float coherent_rms = 0.0f;
    arm_rms_f32(averaged_period, period_samples, &coherent_rms);

    return coherent_rms;
}

/**
 * @brief 打印测量诊断信息
 * @param freq: 测量频率
 * @param adc1_rms: ADC1 RMS值
 * @param adc2_rms: ADC2 RMS值
 * @param phase: 相位差
 * @param snr: 信噪比估计
 */
void PrintMeasurementDiagnostics(float freq, float adc1_rms, float adc2_rms, float phase, float snr)
{
    printf("DIAG: F=%.1fHz, ADC1=%.6fV, ADC2=%.6fV, Phase=%.2f°, SNR=%.1fdB\r\n",
           freq, adc1_rms, adc2_rms, phase, snr);
}

/**
 * @brief 设置输出模式
 * @param mode: 输出模式 (0=简单, 1=详细, 2=调试)
 */
void SetOutputMode(uint8_t mode)
{
    if (mode <= OUTPUT_MODE_DEBUG) {
        output_mode = mode;
        printf("Output mode set to: ");
        switch (mode) {
            case OUTPUT_MODE_SIMPLE:
                printf("SIMPLE (Frequency + Normalized Vpp Ratio)\r\n");
                break;
            case OUTPUT_MODE_DETAILED:
                printf("DETAILED (Frequency + Normalized Ratio + Raw Vpp + Phase)\r\n");
                break;
            case OUTPUT_MODE_DEBUG:
                printf("DEBUG (All information)\r\n");
                break;
        }
    } else {
        printf("Invalid output mode. Valid modes: 0-2\r\n");
    }
}

/**
 * @brief 打印输出模式帮助信息
 */
void PrintOutputModeHelp(void)
{
    printf("=== OUTPUT MODE HELP ===\r\n");
    printf("Mode 0 (SIMPLE): Frequency(Hz) + Normalized_Vpp_Ratio\r\n");
    printf("  Format: F\\tRatio\\r\\n\r\n");
    printf("  Example: 1000.0\\t0.856432\\r\\n\r\n");

    printf("Mode 1 (DETAILED): + Raw Vpp values + Phase\r\n");
    printf("  Format: F\\tRatio\\tVpp1\\tVpp2\\tPhase\\r\\n\r\n");
    printf("  Example: 1000.0\\t0.856432\\t2.145\\t2.506\\t-15.23\\r\\n\r\n");

    printf("Mode 2 (DEBUG): All information with labels\r\n");
    printf("  Format: F=xxx\\tNorm=xxx\\tVpp1=xxx\\tVpp2=xxx\\tPhase=xxx\\tSNR=xxx\\r\\n\r\n");

    printf("Current mode: %d\r\n", output_mode);
    printf("Use SetOutputMode(mode) to change\r\n");
    printf("=== END HELP ===\r\n");
}

/**
 * @brief 处理串口命令
 * @param command: 接收到的命令字符串
 */
void ProcessSerialCommand(char* command)
{
    // 简单的命令解析
    if (strncmp(command, "MODE", 4) == 0) {
        // MODE命令：MODE 0, MODE 1, MODE 2
        if (strlen(command) >= 6) {
            int mode = command[5] - '0'; // 获取数字
            if (mode >= 0 && mode <= 2) {
                SetOutputMode((uint8_t)mode);
            } else {
                printf("Invalid mode. Use MODE 0, MODE 1, or MODE 2\r\n");
            }
        } else {
            printf("Current output mode: %d\r\n", output_mode);
        }
    }
    else if (strncmp(command, "HELP", 4) == 0) {
        // HELP命令
        PrintOutputModeHelp();
    }
    else if (strncmp(command, "STATUS", 6) == 0) {
        // STATUS命令：显示当前状态
        printf("=== SYSTEM STATUS ===\r\n");
        printf("Output Mode: %d\r\n", output_mode);
        printf("Current Frequency: %.1f Hz\r\n", current_frequency);
        printf("Sweep Phase: %d\r\n", sweep_phase);
        printf("Total Sweep Points: %d\r\n", total_sweep_points);
        printf("=== END STATUS ===\r\n");
    }
    else {
        printf("Unknown command: %s\r\n", command);
        printf("Available commands: MODE, HELP, STATUS\r\n");
    }
}